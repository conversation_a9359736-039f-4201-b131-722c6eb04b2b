"use client";
import React, { useState, useEffect, useRef } from "react";
import { Smile, Send, Lock, Trash2 } from "lucide-react";
import { Client, Databases, ID } from "appwrite";
import dynamic from "next/dynamic";
import type { EmojiClickData } from "emoji-picker-react";
import gsap from "gsap";
import { Draggable } from "gsap/Draggable";

// Register the Draggable plugin
gsap.registerPlugin(Draggable);

const EmojiPicker = dynamic(() => import("emoji-picker-react"), { ssr: false });

// Environment variables
const DATABASE_ID = process.env.NEXT_PUBLIC_DB_ID || "";
const COLLECTION_ID = process.env.NEXT_PUBLIC_COLLECTION_ID || "";
const PROJECT_ID = process.env.NEXT_PUBLIC_PROJECT_ID || "";
const ENDPOINT =
  process.env.NEXT_PUBLIC_ENDPOINT || "https://cloud.appwrite.io/v1";
const ADMIN_PASSWORD = process.env.NEXT_PUBLIC_PASSWORD || "";

// Appwrite setup
const client = new Client().setEndpoint(ENDPOINT).setProject(PROJECT_ID);
const databases = new Databases(client);

interface WallMessage {
  id: string;
  text: string;
  timestamp: number;
  color: string;
  border: string;
  shadow: string;
  gradient: string;
  opacity: string;
  position: { x: number; y: number };
  isAdmin: boolean;
  width?: number;
  height?: number;
}

// Enhanced color palette with brighter, more vibrant gradients
const colors = [
  {
    gradient: "from-emerald-500 to-emerald-700",
    border: "border-emerald-500",
    shadow: "shadow-emerald-500",
    bgGradient: "bg-gradient-to-br from-emerald-500/80 to-emerald-700/80",
    opacity: "opacity-95",
  },
  {
    gradient: "from-purple-500 to-indigo-700",
    border: "border-purple-500",
    shadow: "shadow-purple-500",
    bgGradient: "bg-gradient-to-br from-purple-500/80 to-indigo-700/80",
    opacity: "opacity-95",
  },
  {
    gradient: "from-rose-500 to-red-700",
    border: "border-rose-500",
    shadow: "shadow-rose-500",
    bgGradient: "bg-gradient-to-br from-rose-500/80 to-red-700/80",
    opacity: "opacity-95",
  },
  {
    gradient: "from-cyan-500 to-teal-700",
    border: "border-cyan-500",
    shadow: "shadow-cyan-500",
    bgGradient: "bg-gradient-to-br from-cyan-500/80 to-teal-700/80",
    opacity: "opacity-95",
  },
  {
    gradient: "from-amber-500 to-orange-700",
    border: "border-amber-500",
    shadow: "shadow-amber-500",
    bgGradient: "bg-gradient-to-br from-amber-500/80 to-orange-700/80",
    opacity: "opacity-95",
  },
];

const formatDate = (timestamp: number) => {
  const date = new Date(timestamp);
  const now = new Date();
  const isToday = date.toDateString() === now.toDateString();

  // Format time
  const time = date
    .toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    })
    .toLowerCase();

  // If today, show "Today at [time]", otherwise show date and time
  if (isToday) {
    return `Today at ${time}`;
  } else {
    const dateStr = date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
    return `${dateStr} at ${time}`;
  }
};

// Draggable message component
const DraggableMessage = ({
  message,
  updateMessagePosition,
  boardId = "mobile-board", // Default to mobile-board
}: {
  message: WallMessage;
  updateMessagePosition: (
    id: string,
    position: { x: number; y: number }
  ) => void;
  boardId?: string;
}) => {
  const rotationRef = useRef(Math.random() * 4 - 2);
  const messageRef = useRef<HTMLDivElement>(null);
  const draggableRef = useRef<Draggable | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  // Ensure position is valid (not negative or too large)
  const safePosition = {
    x: Math.max(0, message.position.x || 0),
    y: Math.max(0, message.position.y || 0),
  };

  // Initialize GSAP Draggable
  useEffect(() => {
    if (!messageRef.current) return;

    // Clean up previous instance if it exists
    if (draggableRef.current) {
      draggableRef.current.kill();
    }

    // Create Draggable instance with smooth, fluid positioning
    draggableRef.current = Draggable.create(messageRef.current, {
      type: "x,y",
      bounds: `#${boardId}`, // Use the provided board ID for bounds
      edgeResistance: 0.8, // Slightly reduced for smoother edge interaction
      inertia: true, // Enable inertia for natural momentum
      throwProps: {
        // Add subtle momentum when releasing
        resistance: 300,
        minDuration: 0.3,
        maxDuration: 1.2,
      },
      // Enhanced drag settings for fluid movement
      dragClickables: true,
      minimumMovement: 2, // Small threshold to prevent accidental drags
      cursor: "move",
      // Smooth drag resistance for natural feel
      dragResistance: 0.1,
      // Enable momentum-based throwing
      allowEventDefault: false,

      onPress: function () {
        // Immediate subtle pickup effect
        gsap.to(messageRef.current, {
          scale: 1.02,
          boxShadow: "0 8px 25px rgba(0,0,0,0.12)",
          duration: 0.15,
          ease: "power2.out",
          zIndex: 1000,
        });
      },

      onDragStart: function () {
        setIsDragging(true);

        // Enhanced floating effect with smooth transition
        gsap.to(messageRef.current, {
          scale: 1.05,
          boxShadow: "0 12px 30px rgba(0,0,0,0.18)",
          duration: 0.2,
          ease: "power1.out",
          // Add slight rotation for natural feel
          rotation: rotationRef.current * 0.5,
        });
      },

      onDrag: function () {
        // Continuous floating effect during drag
        gsap.to(messageRef.current, {
          boxShadow: "0 15px 35px rgba(0,0,0,0.2)",
          duration: 0.05, // Very quick update for smooth shadow
          ease: "none",
        });
      },

      onDragEnd: function () {
        setIsDragging(false);

        // Natural drop animation with bounce
        gsap.to(messageRef.current, {
          scale: 1,
          boxShadow: "0 4px 12px rgba(0,0,0,0.08)",
          rotation: rotationRef.current,
          duration: 0.4,
          ease: "power2.out",
          zIndex: 1,
          clearProps: "zIndex",
        });

        // Get precise position using GSAP's built-in methods
        if (
          messageRef.current &&
          this.x !== undefined &&
          this.y !== undefined
        ) {
          // Use GSAP's internal position tracking for accuracy
          const newPosition = {
            x: safePosition.x + this.x,
            y: safePosition.y + this.y,
          };

          // Update position in database
          updateMessagePosition(message.id, newPosition);
        }
      },
    })[0];

    // Reset position when message position changes
    draggableRef.current.update();

    return () => {
      if (draggableRef.current) {
        draggableRef.current.kill();
      }
    };
  }, [
    message.id,
    safePosition.x,
    safePosition.y,
    updateMessagePosition,
    boardId,
  ]);

  // Enhanced style with better visual effects and dynamic width
  const style = {
    position: "absolute" as const,
    left: safePosition.x,
    top: safePosition.y,
    transform: `rotate(${rotationRef.current}deg)`,
    zIndex: isDragging ? 1000 : 1,
    width: message.width || 350, // Provide fallback width with larger size for desktop
    touchAction: "none",
    cursor: isDragging ? "grabbing" : "grab",
    transition: "box-shadow 0.3s ease, filter 0.3s ease",
    boxShadow: isDragging
      ? "0 20px 25px rgba(0,0,0,0.25)"
      : message.isAdmin
      ? "0 10px 20px rgba(244,63,94,0.15)" // Rose shadow for admin
      : "0 10px 20px rgba(0,0,0,0.1)",
    filter: isDragging ? "brightness(1.05)" : "brightness(1)",
  };

  // Enhanced function to get background gradient class with brighter, more vibrant colors
  const getBackgroundGradient = (gradientClass: string) => {
    const colorMap = {
      // Match the exact gradient formats from the updated colors array
      "from-emerald-500 to-emerald-700":
        "bg-gradient-to-br from-emerald-500/80 to-emerald-700/80",
      "from-purple-500 to-indigo-700":
        "bg-gradient-to-br from-purple-500/80 to-indigo-700/80",
      "from-rose-500 to-red-700":
        "bg-gradient-to-br from-rose-500/80 to-red-700/80",
      "from-cyan-500 to-teal-700":
        "bg-gradient-to-br from-cyan-500/80 to-teal-700/80",
      "from-amber-500 to-orange-700":
        "bg-gradient-to-br from-amber-500/80 to-orange-700/80",

      // Legacy support for older colors (map to new vibrant colors)
      "from-emerald-800 to-emerald-900":
        "bg-gradient-to-br from-emerald-500/80 to-emerald-700/80",
      "from-purple-800 to-purple-900":
        "bg-gradient-to-br from-purple-500/80 to-indigo-700/80",
      "from-red-900 to-red-950":
        "bg-gradient-to-br from-rose-500/80 to-red-700/80",
      "from-teal-800 to-teal-900":
        "bg-gradient-to-br from-cyan-500/80 to-teal-700/80",

      // More legacy support
      "from-emerald-400 to-emerald-600":
        "bg-gradient-to-br from-emerald-500/80 to-emerald-700/80",
      "from-violet-400 to-purple-600":
        "bg-gradient-to-br from-purple-500/80 to-indigo-700/80",
      "from-blue-400 to-indigo-600":
        "bg-gradient-to-br from-purple-500/80 to-indigo-700/80",
      "from-amber-400 to-orange-600":
        "bg-gradient-to-br from-amber-500/80 to-orange-700/80",
      "from-teal-400 to-cyan-600":
        "bg-gradient-to-br from-cyan-500/80 to-teal-700/80",
      "from-purple-400 to-purple-600":
        "bg-gradient-to-br from-purple-500/80 to-indigo-700/80",
      "from-red-400 to-red-600":
        "bg-gradient-to-br from-rose-500/80 to-red-700/80",
      "from-teal-400 to-teal-600":
        "bg-gradient-to-br from-cyan-500/80 to-teal-700/80",
    };

    // If the gradient class exists in the map, use it; otherwise, pick a random color
    if (colorMap[gradientClass as keyof typeof colorMap]) {
      return colorMap[gradientClass as keyof typeof colorMap];
    } else {
      // For any unrecognized gradient, pick a random vibrant color
      const fallbackColors = [
        "bg-gradient-to-br from-emerald-500/80 to-emerald-700/80",
        "bg-gradient-to-br from-purple-500/80 to-indigo-700/80",
        "bg-gradient-to-br from-rose-500/80 to-red-700/80",
        "bg-gradient-to-br from-cyan-500/80 to-teal-700/80",
        "bg-gradient-to-br from-amber-500/80 to-orange-700/80",
      ];
      return fallbackColors[Math.floor(Math.random() * fallbackColors.length)];
    }
  };

  // Render mobile or desktop message based on width with updated threshold
  const isMobile = message.width && message.width < 220; // Increased threshold for better mobile experience

  return (
    <div ref={messageRef} style={style} className="touch-none select-none">
      {isMobile ? (
        // Mobile message card with enhanced styling and admin distinction
        <div
          className={`backdrop-blur-sm rounded-lg shadow-lg transition-all duration-200 ease-out
            ${
              message.isAdmin
                ? "bg-gradient-to-br from-rose-500/70 to-pink-600/70 border border-rose-400/30 ring-1 ring-rose-400/20"
                : getBackgroundGradient(message.gradient)
            }`}
          style={{
            width: `${message.width}px`,
            padding: message.width && message.width <= 160 ? "12px" : "16px",
          }}
        >
          {/* Message content with improved typography */}
          <p className="text-white/95 break-words text-sm font-medium raleway-font leading-snug mb-3">
            {message.text}
          </p>

          {/* Footer with enhanced admin badge and timestamp */}
          <div className="flex justify-between items-center mt-2 pt-1 border-t border-white/10">
            {message.isAdmin ? (
              <span className="text-[10px] text-white bg-rose-500/50 px-2 py-0.5 rounded-full border border-rose-400/30 font-medium custom">
                Admin
              </span>
            ) : (
              <span className="text-[10px] text-white/60">User</span>
            )}
            <span className="text-[10px] text-white/70">
              {formatDate(message.timestamp)}
            </span>
          </div>
        </div>
      ) : (
        // Enhanced desktop message card with dynamic width
        <div
          className={`rounded-xl p-0.5 group transition-all duration-300 hover:shadow-xl raleway-font
            ${
              message.isAdmin
                ? "bg-gradient-to-br from-rose-500/80 to-pink-600/80 ring-2 ring-rose-400/30 "
                : getBackgroundGradient(message.gradient)
            }`}
          style={{ width: `${message.width}px` }}
        >
          <div
            className={`h-full w-full backdrop-blur-sm rounded-xl p-5 border border-white/10
            ${
              message.isAdmin
                ? "bg-gradient-to-br from-black/30 to-black/10"
                : "bg-gradient-to-br from-black/40 to-black/20"
            }`}
          >
            {/* Admin indicator at top if admin */}
            {message.isAdmin && (
              <div className="flex items-center mb-3 -mt-1">
                <span className="text-xs text-rose-300 bg-rose-500/20 px-2.5 py-0.5 rounded-full border border-rose-400/20 font-medium custom">
                  Admin
                </span>
              </div>
            )}

            {/* Message content with improved typography */}
            <p
              className={`text-white/95 font-medium text-base leading-relaxed mb-4 ${
                message.isAdmin ? "custom" : "raleway-font"
              }`}
            >
              {message.text}
            </p>

            {/* Timestamp with enhanced styling */}
            <div className="flex justify-end items-center">
              <div className="flex items-center space-x-1 text-xs text-white/70 bg-black/30 px-3 py-1 rounded-full border border-white/10">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-white-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 10h.01M12 10h.01M16 10h.01M21 12c0 4.418-4.03 8-9 8a9.77 9.77 0 01-4-.84l-4.38 1.13a1 1 0 01-1.22-1.22l1.13-4.38A9.77 9.77 0 013 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>

                <span>{formatDate(message.timestamp)}</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Board component for GSAP draggable - add margin bottom for laptop
const MessageBoard = ({
  children,
  id,
  boardRef,
}: {
  children: React.ReactNode;
  id: string;
  boardRef: React.RefObject<HTMLDivElement>;
}) => {
  return (
    <div
      id={id}
      ref={boardRef}
      className="relative bg-white/5 rounded-2xl lg:rounded-3xl p-4 lg:p-12 backdrop-blur-xl border border-[#45d6e9]/10 shadow-2xl shadow-[#45d6e9]/5 min-h-[500px] lg:min-h-[600px] overflow-hidden touch-none mb-1 lg:mb-1"
    >
      <div className="absolute inset-0 bg-[linear-gradient(rgba(69,214,233,0.07)_1px,transparent_1px),linear-gradient(90deg,rgba(69,214,233,0.07)_1px,transparent_1px)] bg-[size:20px_20px] rounded-2xl lg:rounded-3xl" />
      {children}
    </div>
  );
};

export default function AnonymousWall() {
  const [messages, setMessages] = useState<WallMessage[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [isAdmin, setIsAdmin] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [emojiPosition, setEmojiPosition] = useState({ top: 0, left: 0 });
  const [dbInitialized, setDbInitialized] = useState(false);
  const [dbError, setDbError] = useState<string | null>(null);
  const boardRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const dragPositionsRef = useRef<Map<string, { x: number; y: number }>>(
    new Map()
  );
  // Initialize with window dimensions instead of zeros
  const [viewportDimensions, setViewportDimensions] = useState({
    width: typeof window !== "undefined" ? window.innerWidth : 1024, // Default to desktop size
    height: typeof window !== "undefined" ? window.innerHeight : 768,
  });
  const [boardDimensions, setBoardDimensions] = useState({
    width: 0,
    height: 0,
    top: 0,
    left: 0,
  });

  // Card dimensions (state to track card sizes for different screen widths)
  const [cardDimensions, setCardDimensions] = useState({
    width: 250,
    height: 150, // Default values, will be updated based on screen size
  });

  // GSAP draggable is initialized in the DraggableMessage component

  // Emoji picker handlers
  const onEmojiClick = (emojiData: EmojiClickData) => {
    setNewMessage((prev) => prev + emojiData.emoji);
    setShowEmojiPicker(false);
  };

  const handleEmojiButtonClick = (event: React.MouseEvent) => {
    event.preventDefault();
    const rect = event.currentTarget.getBoundingClientRect();
    setEmojiPosition({ top: rect.bottom + 5, left: rect.left });
    setShowEmojiPicker((prev) => !prev);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (
        showEmojiPicker &&
        !target.closest('[data-emoji-picker="true"]') &&
        !target.closest(".emoji-button")
      ) {
        setShowEmojiPicker(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showEmojiPicker]);

  // Database initialization
  useEffect(() => {
    if (!DATABASE_ID || !COLLECTION_ID || !PROJECT_ID) {
      setDbError("Missing environment variables");
      return;
    }
    setDbInitialized(true);
    loadMessages();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Update board dimensions when board ref or viewport dimensions change
  useEffect(() => {
    if (boardRef.current) {
      const updateBoardDimensions = () => {
        const rect = boardRef.current?.getBoundingClientRect();
        if (rect) {
          setBoardDimensions({
            width: rect.width,
            height: rect.height,
            top: rect.top,
            left: rect.left,
          });
        }
      };

      // Initial update
      updateBoardDimensions();

      // Add resize observer for more accurate tracking
      const resizeObserver = new ResizeObserver(updateBoardDimensions);
      resizeObserver.observe(boardRef.current);

      return () => {
        if (boardRef.current) {
          // eslint-disable-next-line react-hooks/exhaustive-deps
          resizeObserver.unobserve(boardRef.current);
        }
      };
    }
  }, [boardRef, viewportDimensions]);

  // Add effect to update viewport dimensions on window resize
  useEffect(() => {
    // Function to update viewport dimensions
    const updateViewportDimensions = () => {
      setViewportDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    // Add event listener for resize
    window.addEventListener("resize", updateViewportDimensions);

    // Call once on mount to ensure correct initial dimensions
    updateViewportDimensions();

    // Clean up
    return () => window.removeEventListener("resize", updateViewportDimensions);
  }, []);

  // Define constants for card dimensions based on screen size
  const MESSAGE_WIDTH =
    viewportDimensions.width <= 380
      ? 130
      : viewportDimensions.width <= 1024
      ? 160
      : 250;
  const MESSAGE_HEIGHT =
    viewportDimensions.width <= 380
      ? 100
      : viewportDimensions.width <= 1024
      ? 120
      : 150;

  // Update card dimensions based on screen size using the constants
  useEffect(() => {
    setCardDimensions({
      width: MESSAGE_WIDTH,
      height: MESSAGE_HEIGHT,
    });
  }, [viewportDimensions, MESSAGE_WIDTH, MESSAGE_HEIGHT]);

  // Reposition messages when board dimensions or card dimensions change
  useEffect(() => {
    if (messages.length === 0) return;

    // Only run this effect if we have messages
    const repositionMessages = () => {
      // Get current card dimensions
      const cardWidth = cardDimensions.width;
      const cardHeight = cardDimensions.height;

      // Calculate safe boundaries - use fallbacks if board dimensions aren't ready
      const boardWidth =
        boardDimensions.width > 0
          ? boardDimensions.width
          : window.innerWidth - 40;
      const boardHeight =
        boardDimensions.height > 0
          ? boardDimensions.height
          : window.innerHeight - 200;

      const maxX = Math.max(10, boardWidth - cardWidth - 10); // Leave 10px margin
      const maxY = Math.max(10, boardHeight - cardHeight - 10); // Leave 10px margin

      // Always adjust all messages on small screens
      const isSmallScreen = window.innerWidth <= 768;

      // Check if any messages are outside the boundaries and adjust them
      const adjustedMessages = messages.map((message) => {
        // Get current position with fallbacks
        const currentX =
          typeof message.position.x === "number" ? message.position.x : 0;
        const currentY =
          typeof message.position.y === "number" ? message.position.y : 0;

        // Force reposition on small screens or if outside boundaries
        const needsRepositioning =
          isSmallScreen ||
          currentX > maxX ||
          currentY > maxY ||
          currentX < 10 ||
          currentY < 10;

        if (needsRepositioning) {
          // Calculate new position within boundaries
          const newX = Math.max(10, Math.min(currentX, maxX));
          const newY = Math.max(10, Math.min(currentY, maxY));

          // Update in database for significant changes
          if (Math.abs(newX - currentX) > 5 || Math.abs(newY - currentY) > 5) {
            // Update in database (async, don't await)
            if (dbInitialized) {
              databases
                .updateDocument(DATABASE_ID, COLLECTION_ID, message.id, {
                  position: JSON.stringify({ x: newX, y: newY }),
                })
                .catch((error) => {
                  console.error("Error updating message position:", error);
                });
            }
          }

          // Return message with adjusted position
          return {
            ...message,
            position: { x: newX, y: newY },
            width: cardWidth,
            height: cardHeight,
          };
        }

        // If message is within boundaries, just update dimensions
        return {
          ...message,
          width: cardWidth,
          height: cardHeight,
        };
      });

      // Update messages state with adjusted positions
      setMessages(adjustedMessages);
    };

    // Run the repositioning
    repositionMessages();

    // Also run repositioning on window resize for small screens
    const handleResize = () => {
      if (window.innerWidth <= 768) {
        repositionMessages();
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    boardDimensions.width,
    boardDimensions.height,
    cardDimensions.width,
    cardDimensions.height,
  ]);

  const loadMessages = async () => {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTION_ID
      );

      // Use the MESSAGE_WIDTH and MESSAGE_HEIGHT constants for consistency
      const cardWidth = MESSAGE_WIDTH;
      const cardHeight = MESSAGE_HEIGHT;

      // Calculate safe boundaries (if board dimensions are available)
      const maxX =
        boardDimensions.width > 0
          ? Math.max(0, boardDimensions.width - cardWidth)
          : 500;
      const maxY =
        boardDimensions.height > 0
          ? Math.max(0, boardDimensions.height - cardHeight)
          : 500;

      const loadedMessages = response.documents.map((doc) => {
        // Parse the position from the database
        const originalPosition = JSON.parse(doc.position);

        // Ensure position is within boundaries
        const position = {
          x: Math.max(0, Math.min(originalPosition.x, maxX)),
          y: Math.max(0, Math.min(originalPosition.y, maxY)),
        };

        return {
          id: doc.$id,
          text: doc.text,
          timestamp: doc.timestamp,
          color: doc.color,
          border: doc.border,
          shadow: doc.shadow,
          gradient: doc.gradient,
          opacity: doc.opacity,
          position: position,
          isAdmin: doc.isAdmin || false,
          width: cardWidth,
          height: cardHeight,
        };
      });

      setMessages(loadedMessages);
    } catch (error) {
      console.error("Error loading messages:", error);
      setDbError("Database connection error");
    }
  };

  // Update message position in the database
  const handleMessageDragEnd = async (
    id: string,
    position: { x: number; y: number }
  ) => {
    // Find the message
    const message = messages.find((msg) => msg.id === id);
    if (!message) return;

    // Use actual card dimensions from state
    const cardWidth = cardDimensions.width;
    const cardHeight = cardDimensions.height;

    // Calculate boundaries based on current board dimensions with fallbacks
    const boardWidth =
      boardDimensions.width > 0
        ? boardDimensions.width
        : window.innerWidth - 40;
    const boardHeight =
      boardDimensions.height > 0
        ? boardDimensions.height
        : window.innerHeight - 200;

    // Add margins to prevent sticking to edges
    const maxX = Math.max(10, boardWidth - cardWidth - 10);
    const maxY = Math.max(10, boardHeight - cardHeight - 10);

    // Ensure position stays within boundaries
    const newX = Math.max(10, Math.min(position.x, maxX));
    const newY = Math.max(10, Math.min(position.y, maxY));

    // Important: Update only the dragged message, not all messages
    const newMessages = messages.map((msg) => {
      if (msg.id === id) {
        return {
          ...msg,
          position: { x: newX, y: newY },
        };
      }
      return msg;
    });

    // Set messages once to avoid duplicate updates
    setMessages(newMessages);

    // Update in the database
    try {
      await databases.updateDocument(DATABASE_ID, COLLECTION_ID, id, {
        position: JSON.stringify({ x: newX, y: newY }),
      });
      // After successful update, remove from ref
      dragPositionsRef.current.delete(id);
    } catch (error) {
      console.error("Error updating message position:", error);
      // Position will remain in ref for potential retry
    }
  };

  // Update message position in the database
  const updateMessagePositionInDB = async (
    id: string,
    position: { x: number; y: number }
  ) => {
    if (!dbInitialized) return;

    // Store position in ref for resilience
    dragPositionsRef.current.set(id, position);

    // Update the message position
    handleMessageDragEnd(id, position);
  };

  // Password Modal
  const PasswordModal = () => {
    const [password, setPassword] = useState("");
    const [error, setError] = useState("");

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (password === ADMIN_PASSWORD) {
        setIsAdmin(true);
        setShowPasswordModal(false);
      } else {
        setError("Incorrect password");
      }
    };

    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-black/80 p-6 rounded-xl border border-[#45d6e9]/20 w-[300px]">
          <form onSubmit={handleSubmit} className="space-y-4">
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter admin password"
              className="w-full bg-transparent border border-[#45d6e9]/20 rounded-lg p-2 text-white"
            />
            {error && <p className="text-red-500 text-sm">{error}</p>}
            <div className="flex justify-end gap-2">
              <button
                type="button"
                onClick={() => setShowPasswordModal(false)}
                className="px-4 py-2 text-gray-400 hover:text-white"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-[#45d6e9]/20 text-[#45d6e9] rounded-lg hover:bg-[#45d6e9]/30"
              >
                Login
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  // Message handling
  const addMessage = async () => {
    if (!newMessage.trim() || !dbInitialized) return;

    const randomColor = colors[Math.floor(Math.random() * colors.length)];

    // Use the MESSAGE_WIDTH and MESSAGE_HEIGHT constants for consistency
    const cardWidth = MESSAGE_WIDTH;
    const cardHeight = MESSAGE_HEIGHT;

    // Calculate safe boundaries for new messages with fallbacks
    const boardWidth =
      boardDimensions.width > 0
        ? boardDimensions.width
        : window.innerWidth - 40;
    const boardHeight =
      boardDimensions.height > 0
        ? boardDimensions.height
        : window.innerHeight - 200;

    // Add margins to prevent sticking to edges
    const maxX = Math.max(20, boardWidth - cardWidth - 20);
    const maxY = Math.max(20, boardHeight - cardHeight - 20);

    // For small screens, position more carefully
    const isSmallScreen = window.innerWidth <= 768;
    const position = isSmallScreen
      ? {
          // On small screens, position in a more controlled way
          x: 20 + Math.floor(Math.random() * (maxX - 40)),
          y: 20 + Math.floor(Math.random() * (maxY - 40)),
        }
      : {
          // On larger screens, more random positioning
          x: Math.min(Math.random() * maxX, maxX),
          y: Math.min(Math.random() * maxY, maxY),
        };

    try {
      // Use the bgGradient property for better visual consistency
      const messageData = {
        text: newMessage,
        timestamp: Date.now(),
        color: randomColor.gradient || "",
        border: randomColor.border || "",
        shadow: randomColor.shadow || "",
        gradient: randomColor.gradient || "",
        opacity: randomColor.opacity || "",
        position: JSON.stringify(position),
        isAdmin,
      };

      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTION_ID,
        ID.unique(),
        messageData
      );

      setMessages((prev) => [
        ...prev,
        {
          id: response.$id,
          ...messageData,
          position,
          width: cardWidth,
          height: cardHeight,
        },
      ]);
      setNewMessage("");
    } catch (error) {
      console.error("Error adding message:", error);
    }
  };

  const clearAllMessages = async () => {
    if (!isAdmin || !dbInitialized) return;
    try {
      const messages = await databases.listDocuments(
        DATABASE_ID,
        COLLECTION_ID
      );
      await Promise.all(
        messages.documents.map((doc) =>
          databases.deleteDocument(DATABASE_ID, COLLECTION_ID, doc.$id)
        )
      );
      setMessages([]);
    } catch (error) {
      console.error("Error clearing messages:", error);
    }
  };

  // Viewport handling
  useEffect(() => {
    const updateDimensions = () => {
      setViewportDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };
    updateDimensions();
    window.addEventListener("resize", updateDimensions);
    return () => window.removeEventListener("resize", updateDimensions);
  }, []);

  if (dbError) {
    return (
      <section className="min-h-screen pt-24 px-4 md:px-8 flex items-center justify-center animated-gradient-bg">
        <div className="bg-black/50 backdrop-blur-md p-6 rounded-xl border border-[#45d6e9]/20 max-w-md">
          <h2 className="text-[#45d6e9] text-xl mb-4">Configuration Error</h2>
          <p className="text-white mb-6">{dbError}</p>
          <p className="text-gray-400 text-sm">
            Please check your environment variables configuration.
          </p>
        </div>
      </section>
    );
  }

  return (
    <>
      <section
        id="mysteryboard"
        className="min-h-screen relative pt-6 px-4 md:px-8  overflow-hidden animated-gradient-bg"
      >
        {/* Background elements */}
        <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-40 z-[1]" />
        <div className="absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:15px_15px] opacity-[0.15] z-[1]" />
        <div className="absolute inset-0 bg-[conic-gradient(at_top_left,#45d6e9_10%,transparent_30%,transparent_70%,#45d6e9_90%)] opacity-[0.2] [background-size:20px_20px] scale-[1.2] mix-blend-overlay z-[1]" />

        {/* Mobile Layout */}
        <div className="lg:hidden w-full p-4 relative z-[2] mt-2 mb-12">
          <div className="text-center mb-8 mt-4">
            <h1 className="text-4xl font-bold text-[#45d6e9] mb-2 custom">
              Anonymous Wall
            </h1>
            <p className="text-gray-400 text-base">
              Got thoughts? Share them anonymously.{" "}
            </p>
          </div>
          {/* input field - fixed for small mobile screens */}
          <div className="mb-6">
            <div className="relative flex items-center bg-[#1a1a1a]/60 backdrop-blur-sm rounded-full p-1.5 sm:p-2">
              <input
                ref={inputRef}
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Write something nice..."
                className="flex-1 min-w-0 bg-transparent px-2 sm:px-3 py-1.5 text-gray-200 placeholder-gray-500 focus:outline-none text-sm sm:text-base"
                onKeyDown={(e) => e.key === "Enter" && addMessage()}
              />
              <div className="flex items-center flex-shrink-0">
                <button
                  onClick={handleEmojiButtonClick}
                  className="emoji-button p-1 sm:p-1.5 text-[#45d6e9]"
                >
                  <Smile className="h-4 w-4 sm:h-5 sm:w-5" />
                </button>
                <button
                  onClick={addMessage}
                  className="p-1 sm:p-1.5 text-[#45d6e9]"
                >
                  <Send className="h-4 w-4 sm:h-5 sm:w-5" />
                </button>
              </div>
            </div>
          </div>
          {/* board */}
          <MessageBoard id="mobile-board" boardRef={boardRef}>
            {messages.map((message) => (
              <DraggableMessage
                key={message.id}
                message={message}
                updateMessagePosition={updateMessagePositionInDB}
                boardId="mobile-board"
              />
            ))}
          </MessageBoard>
        </div>

        {/* Desktop Layout */}
        <div className="hidden lg:block max-w-7xl mx-auto w-full p-6 relative z-[2] mb-20">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-[#45d6e9] mb-2 custom">
              Anonymous Wall
            </h1>
            <p className="text-gray-400 text-lg">
              Got thoughts? Share them anonymously.{" "}
            </p>
          </div>
          {/* Responsive input field with proper mobile handling */}
          <div className="mb-8 flex justify-center px-4 sm:px-6 md:px-8">
            <div className="relative flex items-center gap-1 sm:gap-2 md:gap-3 bg-transparent backdrop-blur-sm border border-[#45d6e9]/20 rounded-2xl p-1 sm:p-1.5 md:p-2 w-full max-w-[800px] overflow-hidden">
              {/* Admin badge - only visible on larger screens */}
              {isAdmin && (
                <div className="hidden sm:flex absolute -top-3 -right-3 bg-rose-500/80 text-white text-[10px] px-2 py-0.5 rounded-full shadow-lg">
                  Admin
                </div>
              )}

              <input
                ref={inputRef}
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Write something nice..."
                className="flex-1 bg-transparent px-1 sm:px-2 md:px-4 py-2 text-gray-200 placeholder-gray-500 focus:outline-none text-sm sm:text-base md:text-lg min-w-0"
                onKeyDown={(e) => e.key === "Enter" && addMessage()}
              />

              {/* Send button - always visible */}
              <button
                onClick={addMessage}
                className="flex-shrink-0 p-1 sm:p-1.5 md:p-2 text-[#45d6e9] hover:bg-[#45d6e9]/10 rounded-xl transition-all duration-300"
                aria-label="Send message"
              >
                <Send className="h-4 w-4 sm:h-5 sm:w-5" />
              </button>

              {/* Admin controls - only visible on larger screens */}
              {isAdmin ? (
                <>
                  <button
                    onClick={() => setIsAdmin(false)}
                    className="hidden sm:block flex-shrink-0 p-1 sm:p-1.5 md:p-2 text-rose-400 hover:bg-rose-400/10 rounded-xl"
                    aria-label="Exit admin mode"
                  >
                    <Lock className="h-4 w-4 sm:h-5 sm:w-5" />
                  </button>
                  <button
                    onClick={clearAllMessages}
                    className="hidden sm:block flex-shrink-0 p-1 sm:p-1.5 md:p-2 text-rose-400 hover:bg-rose-400/10 rounded-xl"
                    aria-label="Clear all messages"
                  >
                    <Trash2 className="h-4 w-4 sm:h-5 sm:w-5" />
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setShowPasswordModal(true)}
                  className="hidden sm:block flex-shrink-0 p-1 sm:p-1.5 md:p-2 text-[#45d6e9] hover:bg-[#45d6e9]/10 rounded-xl"
                  aria-label="Admin login"
                >
                  <Lock className="h-4 w-4 sm:h-5 sm:w-5" />
                </button>
              )}

              {/* Emoji button - always visible */}
              <button
                onClick={handleEmojiButtonClick}
                className="emoji-button flex-shrink-0 p-1 sm:p-1.5 md:p-2 text-[#45d6e9] hover:bg-[#45d6e9]/10 rounded-xl"
                aria-label="Add emoji"
              >
                <Smile className="h-4 w-4 sm:h-5 sm:w-5" />
              </button>
            </div>
          </div>
          {/* board */}
          <MessageBoard id="desktop-board" boardRef={boardRef}>
            {messages.map((message) => (
              <DraggableMessage
                key={message.id}
                message={message}
                updateMessagePosition={updateMessagePositionInDB}
                boardId="desktop-board"
              />
            ))}
          </MessageBoard>
        </div>

        {/* Emoji Picker */}
        {showEmojiPicker && (
          <div
            data-emoji-picker="true"
            className="fixed z-[99999]"
            style={{
              top: emojiPosition.top,
              left:
                viewportDimensions.width <= 480 ? "50%" : emojiPosition.left,
              transform:
                viewportDimensions.width <= 480 ? "translateX(-50%)" : "none",
            }}
          >
            <EmojiPicker
              onEmojiClick={onEmojiClick}
              autoFocusSearch={false}
              width={viewportDimensions.width <= 480 ? 300 : 320}
              height={300}
            />
          </div>
        )}

        {/* Password Modal */}
        {showPasswordModal && <PasswordModal />}
      </section>
    </>
  );
}
